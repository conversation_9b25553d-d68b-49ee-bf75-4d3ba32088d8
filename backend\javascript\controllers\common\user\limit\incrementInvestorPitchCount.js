import User from '../../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
    STARTER_INVESTOR_PITCH_LIMIT,
    PRO_INVESTOR_PITCH_LIMIT,
    getLimit,
} from './planConfig.js';

/**
 * @desc    Increment investor pitch generation count for applicable plans
 * @route   POST /api/users/me/increment-pitch-count
 * @access  Private
 */
export const incrementInvestorPitchCount = async (req, res) => {
    try {
        let user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        user = ensureSubscription(user);
        const { planName, subscription } = user;
        const now = new Date();

        if (planName === FREE_TIER_PLAN_NAME_BACKEND) {
            const limit = getLimit(planName, 'investorPitch');
            if (subscription.freeTierInvestorPitchCount >= limit) {
                return res.status(403).json({ message: 'Starter plan investor pitch limit reached. Please upgrade.' });
            }
            subscription.freeTierInvestorPitchCount++;
        } else if (planName === PRO_PLAN_NAME_BACKEND) {
            const limit = getLimit(planName, 'investorPitch');
            let lastReset = subscription.investorPitchMonthlyReset || new Date(0);

            // Check if a month has passed
            const resetDate = new Date(lastReset);
            resetDate.setMonth(resetDate.getMonth() + 1);

            if (now >= resetDate) {
                // If a month has passed, reset the count and the date
                subscription.proTierInvestorPitchCount = 1;
                subscription.investorPitchMonthlyReset = now;
            } else {
                // If still within the month, check the limit
                if (subscription.proTierInvestorPitchCount >= limit) {
                    return res.status(403).json({ message: `Pro plan limit of ${limit} investor pitches per month reached.` });
                }
                subscription.proTierInvestorPitchCount++;
            }
        } else {
            return res.status(400).json({ message: "User plan does not support investor pitch generation." });
        }

        await user.save();

        res.json({
            message: "Investor pitch count successfully incremented.",
            subscription: user.subscription,
        });

    } catch (error) {
        console.error('Increment Investor Pitch Count Error:', error);
        res.status(500).json({ message: 'Server error while incrementing investor pitch count.' });
    }
};

