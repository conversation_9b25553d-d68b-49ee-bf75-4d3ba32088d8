// services/cacheService.js
import NodeCache from 'node-cache';


/**
 * Intelligent caching service for subscription plans
 * Provides automatic cache invalidation and real-time updates
 */
class CacheService {
    constructor() {
        // Create cache instance with 1 hour default TTL
        this.cache = new NodeCache({ 
            stdTTL: 3600, // 1 hour
            checkperiod: 600, // Check for expired keys every 10 minutes
            useClones: false // Don't clone objects for better performance
        });

        // Cache keys
        this.KEYS = {
            SUBSCRIPTION_PLANS: 'subscription_plans',
            SUBSCRIPTION_PLAN_PREFIX: 'subscription_plan_',
            SUBSCRIPTION_STATS: 'subscription_stats',
            PUBLIC_PLANS: 'public_plans'
        };

        // Setup cache event listeners
        this.setupEventListeners();
    }

    /**
     * Setup cache event listeners
     */
    setupEventListeners() {
        this.cache.on('expired', (key, value) => {
            console.log(`Cache: Key "${key}" expired`);
        });

        this.cache.on('del', (key, value) => {
            console.log(`Cache: Key "${key}" deleted`);
        });
    }

    /**
     * Get subscription plans from cache or return null if not cached
     */
    getSubscriptionPlans() {
        return this.cache.get(this.KEYS.SUBSCRIPTION_PLANS);
    }

    /**
     * Set subscription plans in cache
     */
    setSubscriptionPlans(plans, ttl = 3600) {
        console.log(`Cache: Setting subscription plans (${plans.length} plans)`);
        return this.cache.set(this.KEYS.SUBSCRIPTION_PLANS, plans, ttl);
    }

    /**
     * Get specific subscription plan from cache
     */
    getSubscriptionPlan(planName) {
        const key = this.KEYS.SUBSCRIPTION_PLAN_PREFIX + planName;
        return this.cache.get(key);
    }

    /**
     * Set specific subscription plan in cache
     */
    setSubscriptionPlan(planName, plan, ttl = 3600) {
        const key = this.KEYS.SUBSCRIPTION_PLAN_PREFIX + planName;
        console.log(`Cache: Setting subscription plan "${planName}"`);
        return this.cache.set(key, plan, ttl);
    }

    /**
     * Get public plans from cache
     */
    getPublicPlans() {
        return this.cache.get(this.KEYS.PUBLIC_PLANS);
    }

    /**
     * Set public plans in cache
     */
    setPublicPlans(plans, ttl = 1800) { // 30 minutes for public plans
        console.log(`Cache: Setting public plans (${plans.length} plans)`);
        return this.cache.set(this.KEYS.PUBLIC_PLANS, plans, ttl);
    }

    /**
     * Get subscription stats from cache
     */
    getSubscriptionStats() {
        return this.cache.get(this.KEYS.SUBSCRIPTION_STATS);
    }

    /**
     * Set subscription stats in cache
     */
    setSubscriptionStats(stats, ttl = 300) { // 5 minutes for stats
        console.log('Cache: Setting subscription stats');
        return this.cache.set(this.KEYS.SUBSCRIPTION_STATS, stats, ttl);
    }

    /**
     * Invalidate all subscription plan related caches
     */
    invalidateSubscriptionPlans() {
        console.log('Cache: Invalidating all subscription plan caches');
        
        // Delete main plans cache
        this.cache.del(this.KEYS.SUBSCRIPTION_PLANS);
        this.cache.del(this.KEYS.PUBLIC_PLANS);
        
        // Delete individual plan caches
        const keys = this.cache.keys();
        const planKeys = keys.filter(key => key.startsWith(this.KEYS.SUBSCRIPTION_PLAN_PREFIX));
        
        if (planKeys.length > 0) {
            this.cache.del(planKeys);
            console.log(`Cache: Deleted ${planKeys.length} individual plan caches`);
        }

        console.log('Cache: Subscription plan cache invalidated');
    }

    /**
     * Invalidate specific subscription plan cache
     */
    invalidateSubscriptionPlan(planName) {
        console.log(`Cache: Invalidating subscription plan "${planName}"`);
        
        const key = this.KEYS.SUBSCRIPTION_PLAN_PREFIX + planName;
        this.cache.del(key);
        
        // Also invalidate the main plans cache since it contains this plan
        this.cache.del(this.KEYS.SUBSCRIPTION_PLANS);
        this.cache.del(this.KEYS.PUBLIC_PLANS);

        // Notify SSE clients
        sseService.broadcast('cache-invalidated', {
            type: 'subscription-plan',
            planName,
            timestamp: new Date().toISOString(),
            message: `Subscription plan "${planName}" cache invalidated`
        });
    }

    /**
     * Invalidate subscription stats cache
     */
    invalidateSubscriptionStats() {
        console.log('Cache: Invalidating subscription stats');
        this.cache.del(this.KEYS.SUBSCRIPTION_STATS);
    }

    /**
     * Clear all caches
     */
    clearAll() {
        console.log('Cache: Clearing all caches');
        this.cache.flushAll();
        
        console.log('Cache: All caches cleared');
    }

    /**
     * Get cache statistics
     */
    getStats() {
        const stats = this.cache.getStats();
        const keys = this.cache.keys();
        
        return {
            ...stats,
            totalKeys: keys.length,
            subscriptionPlanKeys: keys.filter(key => 
                key === this.KEYS.SUBSCRIPTION_PLANS || 
                key.startsWith(this.KEYS.SUBSCRIPTION_PLAN_PREFIX) ||
                key === this.KEYS.PUBLIC_PLANS
            ).length,
            statsKeys: keys.filter(key => key === this.KEYS.SUBSCRIPTION_STATS).length
        };
    }

    /**
     * Warm up cache with subscription plans
     */
    async warmUp(SubscriptionLimits) {
        try {
            console.log('Cache: Warming up subscription plans cache');
            
            // Initialize default plans if they don't exist
            await SubscriptionLimits.initializeDefaultPlans();
            
            // Fetch and cache all plans
            const plans = await SubscriptionLimits.find({ isActive: true })
                .sort({ sortOrder: 1 });
            
            this.setSubscriptionPlans(plans);
            
            // Cache individual plans
            for (const plan of plans) {
                this.setSubscriptionPlan(plan.planName, plan);
            }
            
            console.log(`Cache: Warmed up with ${plans.length} subscription plans`);
            return true;
        } catch (error) {
            console.error('Cache: Failed to warm up:', error);
            return false;
        }
    }
}

// Create singleton instance
const cacheService = new CacheService();

export default cacheService;
